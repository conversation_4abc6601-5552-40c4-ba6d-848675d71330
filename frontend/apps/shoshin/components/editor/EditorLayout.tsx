"use client"

import { TopToolbar } from "./TopToolbar"
import { LeftSidebar } from "./LeftSidebar"
import { MainCanvas } from "./MainCanvas"
import { RightSidebar } from "./RightSidebar"

export function EditorLayout() {
  return (
    <div className="h-full w-full flex flex-col bg-gray-900">
      {/* Top Toolbar */}
      <TopToolbar />
      
      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar */}
        <LeftSidebar />
        
        {/* Main Canvas */}
        <div className="flex-1 relative">
          <MainCanvas />
        </div>
        
        {/* Right Sidebar */}
        <RightSidebar />
      </div>
    </div>
  )
}
