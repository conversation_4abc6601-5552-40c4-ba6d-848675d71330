"use client"

import {
    Background,
    Controls,
    MiniMap,
    ReactFlow,
    addEdge,
    useEdgesState,
    useNodesState,
    type Connection,
    type Edge,
    type Node,
    type OnConnect,
    type OnDragOver,
    type OnDrop,
} from "@xyflow/react"
import "@xyflow/react/dist/style.css"
import { useCallback, useRef, useState } from "react"
import { CustomNode } from "./CustomNode"

const nodeTypes = {
  custom: CustomNode,
}

const initialNodes: Node[] = [
  {
    id: "start-1",
    type: "custom",
    position: { x: 250, y: 100 },
    data: { 
      label: "Start",
      type: "start",
      description: "Start Workflow"
    },
  },
]

const initialEdges: Edge[] = []

export function MainCanvas() {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null)

  const onConnect: OnConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onDragOver: OnDragOver = useCallback((event) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = "move"
  }, [])

  const onDrop: OnDrop = useCallback(
    (event) => {
      event.preventDefault()

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()
      const data = event.dataTransfer.getData("application/reactflow")

      if (typeof data === "undefined" || !data || !reactFlowBounds) {
        return
      }

      const blockData = JSON.parse(data)
      const position = reactFlowInstance?.screenToFlowPosition({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      })

      const newNode: Node = {
        id: `${blockData.type}-${Date.now()}`,
        type: "custom",
        position,
        data: {
          label: blockData.name,
          type: blockData.type,
          description: blockData.description,
        },
      }

      setNodes((nds) => nds.concat(newNode))
    },
    [reactFlowInstance, setNodes]
  )

  return (
    <div className="w-full h-full" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={setReactFlowInstance}
        onDrop={onDrop}
        onDragOver={onDragOver}
        nodeTypes={nodeTypes}
        fitView
        className="bg-gray-900"
        defaultEdgeOptions={{
          style: { stroke: '#6366f1', strokeWidth: 2 },
          type: 'smoothstep',
        }}
        connectionLineStyle={{ stroke: '#6366f1', strokeWidth: 2 }}
        snapToGrid={true}
        snapGrid={[15, 15]}
      >
        <Background
          color="#374151"
          gap={20}
          size={1}
          variant="dots"
        />
        <Controls
          className="bg-gray-800 border-gray-600 [&>button]:bg-gray-700 [&>button]:border-gray-600 [&>button]:text-gray-300 [&>button:hover]:bg-gray-600"
        />
        <MiniMap
          className="bg-gray-800 border-gray-600"
          nodeColor="#6366f1"
          maskColor="rgba(0, 0, 0, 0.3)"
          pannable
          zoomable
        />
      </ReactFlow>
    </div>
  )
}
