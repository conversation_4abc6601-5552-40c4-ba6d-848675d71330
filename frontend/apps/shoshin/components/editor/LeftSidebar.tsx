"use client"

import { useState } from "react"
import { BlockItem } from "./BlockItem"
import { 
  User, 
  Globe, 
  GitBranch, 
  Code, 
  Route, 
  Database, 
  Brain, 
  Workflow, 
  MessageSquare, 
  RotateCcw 
} from "lucide-react"

const blockCategories = [
  {
    title: "Blocks",
    items: [
      { id: "agent", name: "Agent", description: "Build an agent", icon: User, color: "bg-purple-600" },
      { id: "api", name: "API", description: "Use any API", icon: Globe, color: "bg-blue-600" },
      { id: "condition", name: "Condition", description: "Add a condition", icon: GitBranch, color: "bg-orange-600" },
      { id: "function", name: "Function", description: "Run custom logic", icon: Code, color: "bg-red-600" },
      { id: "router", name: "Router", description: "Route workflow", icon: Route, color: "bg-green-600" },
      { id: "memory", name: "Memory", description: "Add memory store", icon: Database, color: "bg-pink-600" },
      { id: "knowledge", name: "Knowledge", description: "Use vector search", icon: Brain, color: "bg-teal-600" },
      { id: "workflow", name: "Workflow", description: "Execute another workflow", icon: Workflow, color: "bg-amber-600" },
      { id: "response", name: "Response", description: "Send structured API response", icon: MessageSquare, color: "bg-blue-500" },
      { id: "loop", name: "Loop", description: "", icon: RotateCcw, color: "bg-cyan-600" }
    ]
  },
  {
    title: "Tools",
    items: []
  }
]

export function LeftSidebar() {
  const [activeTab, setActiveTab] = useState("Blocks")

  return (
    <div className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
      {/* Search */}
      <div className="p-4 border-b border-gray-700">
        <div className="relative">
          <input
            type="text"
            placeholder="Search..."
            className="w-full bg-gray-700 text-white placeholder-gray-400 px-3 py-2 rounded-md border border-gray-600 focus:border-purple-500 focus:outline-none text-sm"
          />
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-700">
        {blockCategories.map((category) => (
          <button
            key={category.title}
            onClick={() => setActiveTab(category.title)}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === category.title
                ? "text-white bg-gray-700 border-b-2 border-purple-500"
                : "text-gray-400 hover:text-white"
            }`}
          >
            {category.title}
          </button>
        ))}
      </div>

      {/* Block List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {blockCategories
          .find((cat) => cat.title === activeTab)
          ?.items.map((block) => (
            <BlockItem
              key={block.id}
              id={block.id}
              name={block.name}
              description={block.description}
              icon={block.icon}
              color={block.color}
            />
          ))}
      </div>
    </div>
  )
}
